# =============================================================================
# 文件: src/player.py
# 功能: 音频播放模块
# =============================================================================

import os
import time
import threading
import requests
from io import BytesIO
from pathlib import Path
from typing import Optional, Callable
from .config import Config
import tempfile

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    print("⚠️ pygame未安装，将使用系统默认播放器")

try:
    import vlc
    VLC_AVAILABLE = True
except ImportError:
    VLC_AVAILABLE = False

class AudioPlayer:
    """音频播放器"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        self.session = requests.Session()
        self.session.headers.update(self.config.get_headers())
        
        self.is_playing = False
        self.is_paused = False
        self.current_track = None
        self.download_thread = None
        self.play_thread = None
        self.temp_file = None
        
        # 初始化播放器
        self.player = None
        self._init_player()
        
        # 回调函数
        self.on_track_start = None
        self.on_track_end = None
        self.on_download_progress = None
    
    def _init_player(self):
        """初始化播放器"""
        if VLC_AVAILABLE:
            try:
                self.player = vlc.MediaPlayer()
                self.player_type = "vlc"
                print("✓ 使用VLC播放器")
                return
            except Exception as e:
                print(f"VLC初始化失败: {e}")
        
        if PYGAME_AVAILABLE:
            try:
                pygame.mixer.init()
                self.player_type = "pygame"
                print("✓ 使用pygame播放器")
                return
            except Exception as e:
                print(f"pygame初始化失败: {e}")
        
        self.player_type = "system"
        print("✓ 使用系统默认播放器")
    
    def play_with_download(self, track_info: dict, download_dir: str = None) -> bool:
        """边播边下载"""
        if not track_info.get('audio_url'):
            print("❌ 没有音频URL")
            return False
        
        print(f"🎵 开始播放: {track_info.get('track_name', '未知')} - {track_info.get('artist_name', '未知')}")
        
        self.current_track = track_info
        download_dir = download_dir or self.config.DOWNLOAD_DIR
        
        # 生成文件名
        safe_track_name = self._safe_filename(track_info.get('track_name', 'unknown'))
        safe_artist_name = self._safe_filename(track_info.get('artist_name', 'unknown'))
        filename = f"{safe_artist_name} - {safe_track_name}.mp4"
        filepath = os.path.join(download_dir, filename)
        
        # 确保下载目录存在
        os.makedirs(download_dir, exist_ok=True)
        
        if self.config.STREAM_DOWNLOAD:
            # 流式下载并播放
            return self._stream_play_and_download(track_info['audio_url'], filepath)
        else:
            # 先下载后播放
            return self._download_then_play(track_info['audio_url'], filepath)
    
    def _stream_play_and_download(self, audio_url: str, filepath: str) -> bool:
        """流式播放和下载"""
        try:
            # 创建临时文件用于播放
            self.temp_file = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)
            temp_path = self.temp_file.name
            
            # 启动下载线程
            self.download_thread = threading.Thread(
                target=self._download_with_buffer,
                args=(audio_url, filepath, temp_path)
            )
            self.download_thread.daemon = True
            self.download_thread.start()
            
            # 等待缓冲足够的数据
            self._wait_for_buffer(temp_path)
            
            # 开始播放
            if self._start_playback(temp_path):
                self.is_playing = True
                
                # 等待播放完成或下载完成
                self._monitor_playback()
                
                return True
            else:
                return False
                
        except Exception as e:
            print(f"❌ 流式播放失败: {e}")
            return False
    
    def _download_with_buffer(self, audio_url: str, final_path: str, temp_path: str):
        """带缓冲的下载"""
        try:
            response = self.session.get(audio_url, stream=True, timeout=self.config.TIMEOUT)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(temp_path, 'wb') as temp_f, open(final_path, 'wb') as final_f:
                for chunk in response.iter_content(chunk_size=self.config.BUFFER_SIZE):
                    if chunk:
                        # 同时写入临时文件和最终文件
                        temp_f.write(chunk)
                        final_f.write(chunk)
                        temp_f.flush()
                        final_f.flush()
                        
                        downloaded_size += len(chunk)
                        
                        # 回调下载进度
                        if self.on_download_progress and total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            self.on_download_progress(progress)
            
            print(f"✅ 下载完成: {os.path.basename(final_path)}")
            
        except Exception as e:
            print(f"❌ 下载失败: {e}")
    
    def _wait_for_buffer(self, temp_path: str, min_size: int = 1024 * 1024):  # 1MB
        """等待缓冲"""
        print("🔄 等待缓冲...")
        
        start_time = time.time()
        while time.time() - start_time < 30:  # 最多等待30秒
            if os.path.exists(temp_path):
                file_size = os.path.getsize(temp_path)
                if file_size >= min_size:
                    print("✓ 缓冲完成，开始播放")
                    return True
            time.sleep(0.5)
        
        print("⚠️ 缓冲超时，尝试播放")
        return True
    
    def _start_playback(self, file_path: str) -> bool:
        """开始播放"""
        try:
            if self.player_type == "vlc" and self.player:
                media = vlc.Media(file_path)
                self.player.set_media(media)
                self.player.play()
                
                # 等待播放开始
                time.sleep(1)
                return self.player.is_playing()
                
            elif self.player_type == "pygame":
                pygame.mixer.music.load(file_path)
                pygame.mixer.music.play()
                return True
                
            elif self.player_type == "system":
                # 使用系统默认播放器
                if os.name == 'nt':  # Windows
                    os.startfile(file_path)
                elif os.name == 'posix':  # macOS/Linux
                    os.system(f'open "{file_path}"' if os.uname().sysname == 'Darwin' else f'xdg-open "{file_path}"')
                return True
            
        except Exception as e:
            print(f"❌ 播放失败: {e}")
            return False
        
        return False
    
    def _monitor_playback(self):
        """监控播放状态"""
        if self.on_track_start:
            self.on_track_start(self.current_track)
        
        try:
            if self.player_type == "vlc" and self.player:
                # 等待播放结束
                while self.player.is_playing() and self.is_playing:
                    time.sleep(1)
                    
            elif self.player_type == "pygame":
                # 等待播放结束
                while pygame.mixer.music.get_busy() and self.is_playing:
                    time.sleep(1)
            
            # 播放结束
            self.is_playing = False
            if self.on_track_end:
                self.on_track_end(self.current_track)
                
        except Exception as e:
            print(f"监控播放时出错: {e}")
    
    def _download_then_play(self, audio_url: str, filepath: str) -> bool:
        """先下载后播放"""
        try:
            print("🔄 开始下载...")
            
            response = self.session.get(audio_url, timeout=self.config.TIMEOUT)
            response.raise_for_status()
            
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ 下载完成: {os.path.basename(filepath)}")
            
            # 开始播放
            if self._start_playback(filepath):
                self.is_playing = True
                self._monitor_playback()
                return True
            else:
                return False
                
        except Exception as e:
            print(f"❌ 下载播放失败: {e}")
            return False
    
    def pause(self):
        """暂停播放"""
        try:
            if self.player_type == "vlc" and self.player:
                self.player.pause()
            elif self.player_type == "pygame":
                pygame.mixer.music.pause()
            
            self.is_paused = True
            print("⏸️ 已暂停")
            
        except Exception as e:
            print(f"❌ 暂停失败: {e}")
    
    def resume(self):
        """恢复播放"""
        try:
            if self.player_type == "vlc" and self.player:
                self.player.play()
            elif self.player_type == "pygame":
                pygame.mixer.music.unpause()
            
            self.is_paused = False
            print("▶️ 已恢复")
            
        except Exception as e:
            print(f"❌ 恢复失败: {e}")
    
    def stop(self):
        """停止播放"""
        try:
            self.is_playing = False
            
            if self.player_type == "vlc" and self.player:
                self.player.stop()
            elif self.player_type == "pygame":
                pygame.mixer.music.stop()
            
            print("⏹️ 已停止")
            
        except Exception as e:
            print(f"❌ 停止失败: {e}")
    
    def _safe_filename(self, filename: str) -> str:
        """生成安全的文件名"""
        import re
        # 移除或替换不安全的字符
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
        safe_name = safe_name.strip('. ')
        return safe_name[:100]  # 限制长度
    
    def cleanup(self):
        """清理资源"""
        self.stop()
        
        # 清理临时文件
        if self.temp_file:
            try:
                self.temp_file.close()
                if os.path.exists(self.temp_file.name):
                    os.unlink(self.temp_file.name)
            except Exception:
                pass
        
        # 清理播放器
        if self.player_type == "vlc" and self.player:
            self.player.release()
        elif self.player_type == "pygame":
            pygame.mixer.quit()
